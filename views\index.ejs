<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Barang - Aplikasi Kasir</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .barcode-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .table-responsive {
            max-height: 70vh;
        }
        .sticky-header {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }
        .search-box {
            max-width: 400px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-dark bg-primary">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-shopping-cart me-2"></i>
                Aplikasi Kasir - Data Barang
            </span>
            <span class="navbar-text">
                <i class="fas fa-database me-1"></i>
                Total: <%= totalItems.toLocaleString() %> barang
            </span>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Stats Card -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h5><i class="fas fa-boxes me-2"></i><%= totalItems.toLocaleString() %></h5>
                                <small>Total Barang</small>
                            </div>
                            <div class="col-md-3">
                                <h5><i class="fas fa-search me-2"></i><%= search ? 'Hasil Pencarian' : 'Semua Data' %></h5>
                                <small>Status Tampilan</small>
                            </div>
                            <div class="col-md-3">
                                <h5><i class="fas fa-file-alt me-2"></i>Halaman <%= currentPage %></h5>
                                <small>dari <%= totalPages %> halaman</small>
                            </div>
                            <div class="col-md-3">
                                <h5><i class="fas fa-barcode me-2"></i>Barcode Ready</h5>
                                <small>Klik tombol barcode</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Controls -->
        <div class="row mb-3">
            <div class="col-md-6">
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control search-box me-2" 
                           placeholder="Cari berdasarkan kode atau nama barang..." 
                           value="<%= search %>">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Cari
                    </button>
                    <% if (search) { %>
                    <a href="/" class="btn btn-secondary ms-2">
                        <i class="fas fa-times"></i> Reset
                    </a>
                    <% } %>
                </form>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group">
                    <button class="btn btn-outline-primary" onclick="changeLimit(25)">25</button>
                    <button class="btn btn-outline-primary" onclick="changeLimit(50)">50</button>
                    <button class="btn btn-outline-primary" onclick="changeLimit(100)">100</button>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Data Barang</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark sticky-header">
                            <tr>
                                <th style="width: 5%">#</th>
                                <th style="width: 15%">Kode</th>
                                <th style="width: 35%">Nama Barang</th>
                                <th style="width: 10%">Satuan</th>
                                <th style="width: 8%">Stok</th>
                                <th style="width: 12%">Harga Jual</th>
                                <th style="width: 15%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% barang.forEach((item, index) => { %>
                            <tr>
                                <td><%= ((currentPage - 1) * limit) + index + 1 %></td>
                                <td>
                                    <code class="text-primary"><%= item.kode %></code>
                                </td>
                                <td>
                                    <strong><%= item.nama %></strong>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><%= item.satuan %></span>
                                </td>
                                <td>
                                    <% if (item.stok > 0) { %>
                                        <span class="badge bg-success"><%= item.stok %></span>
                                    <% } else { %>
                                        <span class="badge bg-danger">0</span>
                                    <% } %>
                                </td>
                                <td>
                                    <strong class="text-success">Rp <%= parseInt(item.hargaJual).toLocaleString() %></strong>
                                </td>
                                <td>
                                    <button class="btn btn-primary btn-sm barcode-btn me-1" 
                                            onclick="showBarcode('<%= item.kode %>')">
                                        <i class="fas fa-barcode"></i> Barcode
                                    </button>
                                    <a href="/barang/<%= item.kode %>" class="btn btn-info btn-sm barcode-btn">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <% if (totalPages > 1) { %>
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                <% if (hasPrev) { %>
                <li class="page-item">
                    <a class="page-link" href="?page=<%= currentPage - 1 %>&limit=<%= limit %>&search=<%= search %>">
                        <i class="fas fa-chevron-left"></i> Sebelumnya
                    </a>
                </li>
                <% } %>
                
                <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                    <a class="page-link" href="?page=<%= i %>&limit=<%= limit %>&search=<%= search %>"><%= i %></a>
                </li>
                <% } %>
                
                <% if (hasNext) { %>
                <li class="page-item">
                    <a class="page-link" href="?page=<%= currentPage + 1 %>&limit=<%= limit %>&search=<%= search %>">
                        Selanjutnya <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <% } %>
            </ul>
        </nav>
        <% } %>
    </div>

    <!-- Barcode Modal -->
    <div class="modal fade" id="barcodeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-barcode me-2"></i>Barcode Barang
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="barcodeContent">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Generating barcode...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    <button type="button" class="btn btn-primary" onclick="printBarcode()">
                        <i class="fas fa-print"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        function showBarcode(kode) {
            const modal = new bootstrap.Modal(document.getElementById('barcodeModal'));
            const content = document.getElementById('barcodeContent');
            
            // Reset content
            content.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Generating barcode...</p>
            `;
            
            modal.show();
            
            // Fetch barcode data
            fetch(`/barcode/${kode}`)
                .then(response => response.json())
                .then(data => {
                    content.innerHTML = `
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">${data.nama}</h5>
                                <p class="card-text">
                                    <strong>Kode:</strong> ${data.kode}<br>
                                    <strong>Harga:</strong> <span class="text-success">Rp ${parseInt(data.hargaJual).toLocaleString()}</span><br>
                                    <strong>Stok:</strong> ${data.stok} ${data.satuan}
                                </p>
                                <svg id="barcode-${kode}" class="img-fluid" style="max-width: 300px;"></svg>
                            </div>
                        </div>
                    `;

                    // Generate barcode using JsBarcode
                    JsBarcode(`#barcode-${kode}`, data.kode, {
                        format: "CODE128",
                        width: 2,
                        height: 60,
                        displayValue: true,
                        fontSize: 12,
                        textMargin: 5
                    });
                })
                .catch(error => {
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error generating barcode: ${error.message}
                        </div>
                    `;
                });
        }
        
        function printBarcode() {
            const content = document.getElementById('barcodeContent').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Print Barcode</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; }
                            .card { border: 1px solid #ddd; padding: 20px; margin: 20px; }
                        </style>
                    </head>
                    <body>${content}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
        
        function changeLimit(newLimit) {
            const url = new URL(window.location);
            url.searchParams.set('limit', newLimit);
            url.searchParams.set('page', 1);
            window.location = url;
        }
    </script>
</body>
</html>
