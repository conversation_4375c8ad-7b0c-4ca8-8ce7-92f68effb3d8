const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'cashier'
};

async function checkTableStructure() {
  let connection;
  
  try {
    console.log('=== CEK STRUKTUR TABEL BARANG ===');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ Koneksi berhasil');
    
    // Cek struktur tabel
    const [columns] = await connection.query('DESCRIBE barang');
    console.log('\n📋 Struktur tabel barang:');
    columns.forEach(col => {
      console.log(`   ${col.Field} - ${col.Type} ${col.Null === 'NO' ? '(NOT NULL)' : ''} ${col.Key ? `(${col.Key})` : ''} ${col.Default !== null ? `DEFAULT: ${col.Default}` : ''}`);
    });
    
    console.log('\n✅ Selesai!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkTableStructure();
