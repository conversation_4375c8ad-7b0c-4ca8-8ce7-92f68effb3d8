const xlsx = require('xlsx');
const fs = require('fs');

console.log('=== PEMBERSIH DATA EXCEL BARANG (VERSI IMPROVED) ===');
console.log('<PERSON><PERSON><PERSON> proses...');

let rawData;
try {
  const workbook = xlsx.readFile('data/barangAda2.xls');
  console.log('✓ File Excel berhasil dibaca');
  
  const sheetName = workbook.SheetNames[0];
  const sheet = workbook.Sheets[sheetName];
  console.log('✓ Mengkonversi sheet ke JSON...');
  rawData = xlsx.utils.sheet_to_json(sheet, { header: 1, defval: "" });
  console.log(`✓ Total baris data: ${rawData.length}`);
} catch (error) {
  console.error('❌ Error membaca file Excel:', error);
  process.exit(1);
}

console.log('Mulai memproses data...');

let cleanedData = [];
let lastRow = null;
let skippedRows = 0;
let processedRows = 0;

rawData.forEach(row => {
  // Row struktur: [empty, kode, nama barang, satuan, stok, harga beli, empty, harga jual]
  if (!row || row.length === 0) return; // skip kosong
  
  // Gabung jadi string buat cek kata-kata khusus
  let rowText = row.join(" ").trim();

  // Skip baris info halaman / tanggal cetak (lebih ketat)
  if (rowText.includes("Dicetak Tanggal") || 
      rowText.includes("Halaman") ||
      rowText.includes("Dicetak pada") ||
      rowText.includes("Page") ||
      rowText.match(/^\d{1,2}\/\d{1,2}\/\d{4}/) || // format tanggal
      rowText.match(/^Halaman\s*\d+/) || // format halaman
      rowText.match(/^Dicetak\s*Tanggal/)) {
    skippedRows++;
    return;
  }
  
  // Skip header dan kategori
  if (rowText.includes("LBV SMART") || 
      rowText.includes("Daftar Barang") || 
      rowText.includes("Kode") || 
      rowText.includes("Nama Barang") ||
      rowText.includes("ACCESSORIES") || 
      rowText.includes("AKSESORIS") ||
      rowText.match(/^[A-Z\s\(\)]+$/)) { // skip kategori yang hanya huruf besar
    skippedRows++;
    return;
  }
  
  // Jika ada kode di kolom 1 (index 1) dan nama di kolom 2 (index 2)
  if (row[1] && row[2] && (typeof row[1] === 'number' || typeof row[1] === 'string')) {
    // Pastikan ini bukan header dengan mengecek apakah kolom 3 ada satuan
    if (row[3] && typeof row[3] === 'string' && row[3].trim() !== '') {
      // Validasi kode tidak kosong dan satuan tidak kosong
      const kode = String(row[1]).trim();
      const nama = String(row[2] || "").trim();
      const satuan = String(row[3] || "").trim();
      
      if (kode && nama && satuan) {
        // Potong nama jika terlalu panjang (maksimal 100 karakter)
        let namaFinal = nama;
        if (nama.length > 100) {
          namaFinal = nama.substring(0, 97) + "...";
        }
        
        // Simpan sebagai baris baru
        lastRow = {
          kode: kode,
          nama: namaFinal,
          satuan: satuan,
          stok: row[4] || 0,
          hargaBeli: row[5] || 0,
          hargaJual: row[6] || row[7] || 0 // kadang ada kolom kosong di tengah
        };
        cleanedData.push(lastRow);
        processedRows++;
      } else {
        skippedRows++;
      }
    } else {
      skippedRows++;
    }
  } else if (!row[1] && row[2] && lastRow) {
    // Baris lanjutan nama barang (kolom 1 kosong, tapi ada nama di kolom 2)
    const tambahan = String(row[2] || "").trim();
    if (tambahan && lastRow.nama.length + tambahan.length + 1 <= 100) {
      lastRow.nama += " " + tambahan;
    }
    // Jika menambahkan akan melebihi 100 karakter, abaikan
  } else {
    skippedRows++;
  }
});

console.log('✓ Selesai memproses data.');
console.log(`✓ Total barang yang berhasil diproses: ${cleanedData.length}`);
console.log(`✓ Baris yang dilewati: ${skippedRows}`);

// Validasi final - hapus barang dengan data tidak lengkap
const validData = cleanedData.filter(item => {
  return item.kode && 
         item.nama && 
         item.satuan && 
         item.kode.trim() !== '' && 
         item.nama.trim() !== '' && 
         item.satuan.trim() !== '';
});

console.log(`✓ Barang valid setelah filter: ${validData.length}`);
console.log(`✓ Barang yang dihapus karena data tidak lengkap: ${cleanedData.length - validData.length}`);

// Simpan hasil ke file JSON
fs.writeFileSync('barang_clean_improved.json', JSON.stringify(validData, null, 2), 'utf-8');
console.log('✓ File barang_clean_improved.json berhasil dibuat.');

// Tampilkan statistik
console.log('\n=== STATISTIK HASIL ===');
console.log(`Total barang valid: ${validData.length}`);
console.log(`Barang dengan nama panjang (>50 karakter): ${validData.filter(item => item.nama.length > 50).length}`);
console.log(`Barang dengan nama sangat panjang (>80 karakter): ${validData.filter(item => item.nama.length > 80).length}`);
console.log(`Barang dengan stok 0: ${validData.filter(item => item.stok === 0).length}`);
console.log(`Barang dengan harga jual 0: ${validData.filter(item => item.hargaJual === 0).length}`);

// Tampilkan contoh barang dengan nama panjang
const namaPanjang = validData.filter(item => item.nama.length > 80).slice(0, 5);
if (namaPanjang.length > 0) {
  console.log('\n=== CONTOH BARANG DENGAN NAMA PANJANG ===');
  namaPanjang.forEach((item, index) => {
    console.log(`${index + 1}. ${item.kode} - ${item.nama} (${item.nama.length} karakter)`);
  });
}

console.log('\n✅ Proses selesai! Data siap digunakan.');
console.log('📁 File output: barang_clean_improved.json');
