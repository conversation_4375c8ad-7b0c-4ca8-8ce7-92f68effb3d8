const mysql = require('mysql2/promise');

// Konfigurasi database - SESUAIKAN DENGAN SETTING MYSQL ANDA
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Ganti dengan password MySQL Anda
  port: 3306
};

async function testConnection() {
  let connection;
  
  try {
    console.log('=== TEST KONEKSI MYSQL ===');
    console.log('🔌 Mencoba koneksi ke MySQL...');
    console.log(`   Host: ${dbConfig.host}`);
    console.log(`   User: ${dbConfig.user}`);
    console.log(`   Port: ${dbConfig.port}`);
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Koneksi MySQL berhasil!');
    
    // Cek versi MySQL
    const [rows] = await connection.query('SELECT VERSION() as version');
    console.log(`✓ MySQL Version: ${rows[0].version}`);
    
    // Cek database yang ada
    const [databases] = await connection.query('SHOW DATABASES');
    console.log('\n📋 Database yang tersedia:');
    databases.forEach(db => {
      console.log(`   - ${db.Database}`);
    });

    // Cek apakah database cashier sudah ada
    const cashierExists = databases.some(db => db.Database === 'cashier');
    if (cashierExists) {
      console.log('\n✅ Database "cashier" sudah ada');

      // Cek tabel barang
      await connection.query('USE cashier');
      const [tables] = await connection.query('SHOW TABLES');

      if (tables.length > 0) {
        console.log('📋 Tabel yang ada di database cashier:');
        tables.forEach(table => {
          console.log(`   - ${Object.values(table)[0]}`);
        });

        // Cek apakah tabel barang ada
        const barangExists = tables.some(table => Object.values(table)[0] === 'barang');
        if (barangExists) {
          const [count] = await connection.query('SELECT COUNT(*) as total FROM barang');
          console.log(`✅ Tabel "barang" sudah ada dengan ${count[0].total} data`);
        } else {
          console.log('⚠️ Tabel "barang" belum ada');
        }
      } else {
        console.log('⚠️ Database cashier kosong (belum ada tabel)');
      }
    } else {
      console.log('\n⚠️ Database "cashier" belum ada');
    }
    
    console.log('\n🎉 Test koneksi berhasil!');
    console.log('\n📝 LANGKAH SELANJUTNYA:');
    
    if (!cashierExists) {
      console.log('1. Jalankan: node setup_database.js');
      console.log('2. Jalankan: node import_to_mysql.js');
    } else {
      console.log('1. Jalankan: node import_to_mysql.js');
    }
    
  } catch (error) {
    console.error('\n❌ Koneksi gagal:', error.message);
    
    console.log('\n🔧 TROUBLESHOOTING:');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ MySQL server tidak berjalan');
      console.log('💡 Solusi:');
      console.log('   - Jalankan XAMPP/WAMP dan start MySQL');
      console.log('   - Atau jalankan: net start mysql (Windows)');
      console.log('   - Atau jalankan: sudo service mysql start (Linux)');
      
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('❌ Username/password salah');
      console.log('💡 Solusi:');
      console.log('   - Edit file ini dan sesuaikan username/password');
      console.log('   - Default XAMPP: user="root", password=""');
      console.log('   - Default WAMP: user="root", password=""');
      
    } else if (error.code === 'ENOTFOUND') {
      console.log('❌ Host tidak ditemukan');
      console.log('💡 Solusi:');
      console.log('   - Pastikan host="localhost" atau IP yang benar');
      
    } else {
      console.log(`❌ Error lain: ${error.code}`);
      console.log('💡 Periksa konfigurasi MySQL Anda');
    }
    
    console.log('\n📋 KONFIGURASI SAAT INI:');
    console.log(`   Host: ${dbConfig.host}`);
    console.log(`   User: ${dbConfig.user}`);
    console.log(`   Password: ${dbConfig.password ? '[SET]' : '[EMPTY]'}`);
    console.log(`   Port: ${dbConfig.port}`);
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Koneksi ditutup');
    }
  }
}

// Jalankan test
testConnection();
