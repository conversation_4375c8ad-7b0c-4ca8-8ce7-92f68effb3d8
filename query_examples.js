const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'cashier'
};

async function queryExamples() {
  let connection;
  
  try {
    console.log('=== CONTOH QUERY UNTUK APLIKASI KASIR ===');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ Koneksi berhasil\n');
    
    // 1. Cari barang berdasarkan kode
    console.log('1. CARI BARANG BERDASARKAN KODE');
    const kodeBarang = '18997210107577';
    const [barangByKode] = await connection.query('SELECT * FROM barang WHERE kode = ?', [kodeBarang]);
    if (barangByKode.length > 0) {
      const item = barangByKode[0];
      console.log(`   Kode: ${item.kode}`);
      console.log(`   Nama: ${item.nama}`);
      console.log(`   Stok: ${item.stok}`);
      console.log(`   Harga: Rp ${parseInt(item.hargaJual).toLocaleString()}`);
    }
    
    // 2. Cari barang berdasarkan nama (LIKE)
    console.log('\n2. CARI BARANG BERDASARKAN NAMA (mengandung "gunting")');
    const [barangByNama] = await connection.query('SELECT * FROM barang WHERE nama LIKE ? LIMIT 5', ['%gunting%']);
    barangByNama.forEach((item, index) => {
      console.log(`   ${index + 1}. [${item.kode}] ${item.nama} - Stok: ${item.stok}`);
    });
    
    // 3. Barang dengan stok rendah (< 10)
    console.log('\n3. BARANG DENGAN STOK RENDAH (< 10)');
    const [stokRendah] = await connection.query('SELECT * FROM barang WHERE stok > 0 AND stok < 10 ORDER BY stok ASC LIMIT 5');
    stokRendah.forEach((item, index) => {
      console.log(`   ${index + 1}. [${item.kode}] ${item.nama} - Stok: ${item.stok}`);
    });
    
    // 4. Barang berdasarkan kategori harga
    console.log('\n4. BARANG BERDASARKAN KATEGORI HARGA');
    
    // Barang murah (< Rp 10.000)
    const [barangMurah] = await connection.query('SELECT COUNT(*) as total FROM barang WHERE hargaJual < 10000');
    console.log(`   Barang < Rp 10.000: ${barangMurah[0].total.toLocaleString()}`);
    
    // Barang menengah (Rp 10.000 - Rp 100.000)
    const [barangMenengah] = await connection.query('SELECT COUNT(*) as total FROM barang WHERE hargaJual >= 10000 AND hargaJual <= 100000');
    console.log(`   Barang Rp 10.000 - Rp 100.000: ${barangMenengah[0].total.toLocaleString()}`);
    
    // Barang mahal (> Rp 100.000)
    const [barangMahal] = await connection.query('SELECT COUNT(*) as total FROM barang WHERE hargaJual > 100000');
    console.log(`   Barang > Rp 100.000: ${barangMahal[0].total.toLocaleString()}`);
    
    // 5. Update stok barang (simulasi penjualan)
    console.log('\n5. SIMULASI UPDATE STOK (penjualan 1 unit)');
    const kodeJual = '18997210107577';
    
    // Cek stok sebelum
    const [stokSebelum] = await connection.query('SELECT stok FROM barang WHERE kode = ?', [kodeJual]);
    console.log(`   Stok sebelum: ${stokSebelum[0].stok}`);
    
    // Update stok (kurangi 1)
    await connection.query('UPDATE barang SET stok = stok - 1 WHERE kode = ? AND stok > 0', [kodeJual]);
    
    // Cek stok sesudah
    const [stokSesudah] = await connection.query('SELECT stok FROM barang WHERE kode = ?', [kodeJual]);
    console.log(`   Stok sesudah: ${stokSesudah[0].stok}`);
    
    // Kembalikan stok (untuk demo)
    await connection.query('UPDATE barang SET stok = stok + 1 WHERE kode = ?', [kodeJual]);
    console.log(`   Stok dikembalikan untuk demo`);
    
    // 6. Laporan penjualan (simulasi)
    console.log('\n6. LAPORAN NILAI INVENTORY');
    const [laporanInventory] = await connection.query(`
      SELECT 
        COUNT(*) as total_item,
        SUM(stok) as total_stok,
        SUM(stok * hargaBeli) as nilai_beli,
        SUM(stok * hargaJual) as nilai_jual,
        SUM(stok * (hargaJual - hargaBeli)) as potensi_profit
      FROM barang 
      WHERE stok > 0
    `);
    
    const laporan = laporanInventory[0];
    console.log(`   Total item dengan stok: ${laporan.total_item.toLocaleString()}`);
    console.log(`   Total stok: ${laporan.total_stok.toLocaleString()}`);
    console.log(`   Nilai beli: Rp ${parseInt(laporan.nilai_beli).toLocaleString()}`);
    console.log(`   Nilai jual: Rp ${parseInt(laporan.nilai_jual).toLocaleString()}`);
    console.log(`   Potensi profit: Rp ${parseInt(laporan.potensi_profit).toLocaleString()}`);
    
    // 7. Top 10 barang termahal
    console.log('\n7. TOP 10 BARANG TERMAHAL');
    const [barangTermahal] = await connection.query('SELECT kode, nama, hargaJual FROM barang ORDER BY hargaJual DESC LIMIT 10');
    barangTermahal.forEach((item, index) => {
      console.log(`   ${index + 1}. [${item.kode}] ${item.nama.substring(0, 40)}... - Rp ${parseInt(item.hargaJual).toLocaleString()}`);
    });
    
    // 8. Barang berdasarkan satuan
    console.log('\n8. BARANG BERDASARKAN SATUAN');
    const [satuanStats] = await connection.query('SELECT satuan, COUNT(*) as total FROM barang GROUP BY satuan ORDER BY total DESC LIMIT 10');
    satuanStats.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.satuan}: ${item.total.toLocaleString()} barang`);
    });
    
    console.log('\n✅ Contoh query selesai!');
    console.log('\n💡 Query ini bisa digunakan untuk:');
    console.log('   - Aplikasi kasir/POS');
    console.log('   - Sistem inventory');
    console.log('   - Laporan penjualan');
    console.log('   - Manajemen stok');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Koneksi ditutup');
    }
  }
}

queryExamples();
