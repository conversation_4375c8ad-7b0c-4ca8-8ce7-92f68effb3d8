# Pembersih Data Excel Barang

Aplikasi Node.js untuk membersihkan dan memproses data barang dari file Excel (.xls) menjadi format JSON yang siap digunakan.

## Masalah yang Diselesaikan

1. ✅ **Hasil Excel kosong** - Diperbaiki dengan parsing struktur Excel yang benar
2. ✅ **Menghapus baris tanggal cetak dan halaman** - Filter otomatis untuk baris yang tidak diperlukan
3. ✅ **Nama barang terlalu panjang** - Otomatis memotong nama > 100 karakter dan menggabungkan baris lanjutan
4. ✅ **Kode dan satuan kosong** - Validasi data untuk memastikan field penting tidak kosong

## File yang Tersedia

### 1. `main.js` - Versi Dasar
Script pembersih data dasar yang berhasil memproses 41,568 barang dari file Excel.

### 2. `main_improved.js` - Versi Improved ⭐
Script yang telah diperbaiki dengan fitur tambahan:
- Filter lebih ketat untuk baris tanggal/halaman
- Validasi data yang lebih baik
- Pemotongan nama barang yang terlalu panjang
- Statistik hasil yang lebih detail

### 3. `check_data.js` - Analisis Data
Script untuk menganalisis hasil dan mendeteksi masalah dalam data.

## Cara Penggunaan

### Persiapan
```bash
npm install xlsx
```

### Menjalankan Pembersih Data
```bash
# Versi improved (direkomendasikan)
node main_improved.js

# Versi dasar
node main.js
```

### Menganalisis Hasil
```bash
node check_data.js
```

## Struktur Data Input (Excel)

File Excel memiliki struktur:
- Kolom A: Kosong
- Kolom B: Kode Barang
- Kolom C: Nama Barang
- Kolom D: Satuan
- Kolom E: Stok
- Kolom F: Harga Beli
- Kolom G: Kosong (kadang)
- Kolom H: Harga Jual

## Struktur Data Output (JSON)

```json
{
  "kode": "18997210107577",
  "nama": "gunting montana STI 165",
  "satuan": "Buah",
  "stok": 466,
  "hargaBeli": 4300,
  "hargaJual": 4500
}
```

## Hasil Pemrosesan

### Statistik Terakhir:
- **Total barang berhasil diproses**: 41,568
- **Baris yang dilewati**: 2,682 (header, kategori, tanggal, dll)
- **Barang dengan nama panjang (>50 karakter)**: 176
- **Barang dengan stok 0**: 3,978
- **Kode barang duplikat**: 87
- **Barang tanpa harga jual**: 1

### Fitur Pembersihan:
1. **Menghapus baris tidak perlu**:
   - Header ("LBV SMART", "Daftar Barang")
   - Kategori ("ACCESSORIES", "AKSESORIS")
   - Tanggal cetak dan halaman
   - Baris kosong

2. **Validasi data**:
   - Kode barang tidak boleh kosong
   - Nama barang tidak boleh kosong
   - Satuan tidak boleh kosong

3. **Normalisasi nama**:
   - Menggabungkan baris lanjutan nama barang
   - Memotong nama yang terlalu panjang (>100 karakter)
   - Membersihkan spasi berlebih

4. **Penanganan harga**:
   - Menggunakan kolom harga jual yang tepat
   - Default 0 untuk harga kosong

## File Output

- `barang_clean.json` - Hasil dari main.js
- `barang_clean_improved.json` - Hasil dari main_improved.js (direkomendasikan)

## Troubleshooting

### Jika hasil masih kosong:
1. Pastikan file `data/barangAda2.xls` ada
2. Periksa struktur Excel apakah sesuai
3. Jalankan dengan `node main_improved.js` untuk hasil terbaik

### Jika ada error:
1. Pastikan Node.js terinstall
2. Install dependency: `npm install xlsx`
3. Periksa permission file

## Import ke Database MySQL

### Persiapan Database
```bash
# Install dependency MySQL
npm install mysql2

# Test koneksi MySQL
node test_mysql_connection.js

# Setup database dan tabel (jika belum ada)
node setup_database.js
```

### Import Data
```bash
# Import data JSON ke MySQL
node import_to_mysql.js

# Verifikasi hasil import
node verify_import.js
```

### Konfigurasi Database
- **Database**: `cashier`
- **Tabel**: `barang`
- **Total Data**: 41,568 barang
- **Struktur Tabel**:
  ```sql
  CREATE TABLE barang (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kode VARCHAR(50) NOT NULL UNIQUE,
    nama VARCHAR(255) NOT NULL,
    satuan VARCHAR(50) NOT NULL,
    stok INT DEFAULT 0,
    hargaBeli DECIMAL(15,2) DEFAULT 0,
    hargaJual DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  );
  ```

## Contoh Penggunaan Data

### Dari File JSON:
```javascript
const fs = require('fs');
const barang = JSON.parse(fs.readFileSync('barang_clean_improved.json', 'utf-8'));

// Cari barang berdasarkan kode
const item = barang.find(b => b.kode === '18997210107577');

// Filter barang dengan stok > 0
const tersedia = barang.filter(b => b.stok > 0);

// Hitung total nilai stok
const totalNilai = barang.reduce((sum, b) => sum + (b.stok * b.hargaBeli), 0);
```

### Dari Database MySQL:
```javascript
const mysql = require('mysql2/promise');

const connection = await mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'cashier'
});

// Cari barang berdasarkan kode
const [rows] = await connection.query('SELECT * FROM barang WHERE kode = ?', ['18997210107577']);

// Filter barang dengan stok > 0
const [tersedia] = await connection.query('SELECT * FROM barang WHERE stok > 0');

// Hitung total nilai stok
const [total] = await connection.query('SELECT SUM(stok * hargaBeli) as total FROM barang');
```

## Statistik Data

- **Total barang**: 41,568
- **Barang dengan stok > 0**: 37,586
- **Barang dengan stok = 0**: 3,978
- **Barang dengan harga > Rp 100.000**: 2,694
- **Total nilai stok**: Rp 206.468.512.552

## Catatan Penting

- Data sudah dibersihkan dari baris tanggal cetak dan halaman
- Nama barang panjang sudah dipotong untuk menghindari masalah display
- Semua barang memiliki kode, nama, dan satuan yang valid
- Data tersedia dalam format JSON dan MySQL database
- File JSON dan database MySQL siap digunakan untuk aplikasi kasir atau inventory
