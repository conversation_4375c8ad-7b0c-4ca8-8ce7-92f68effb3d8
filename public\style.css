/* Custom CSS untuk Aplik<PERSON> */

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hover Effects */
.table tbody tr:hover {
    background-color: #f8f9fa !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* Button Animations */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Card Animations */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Barcode Modal Styling */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-radius: 15px 15px 0 0;
}

/* Search Box Styling */
.search-box {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.search-box:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    transform: scale(1.02);
}

/* Stats Card Animation */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Badge Styling */
.badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
    border-radius: 20px;
}

/* Table Responsive Improvements */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* Pagination Styling */
.pagination .page-link {
    border-radius: 25px;
    margin: 0 2px;
    border: none;
    color: #007bff;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #007bff;
    color: white;
    transform: scale(1.1);
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    transform: scale(1.1);
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.9em;
    }
    
    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }
    
    .stats-card .row > div {
        margin-bottom: 1rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .barcode-print {
        page-break-inside: avoid;
        text-align: center;
        padding: 20px;
        border: 2px solid #000;
        margin: 20px;
    }
}

/* Success/Error Messages */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

/* Barcode Container */
.barcode-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

/* Loading Spinner */
.spinner-border {
    animation: spin 1s linear infinite;
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Navbar Improvements */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Code Styling */
code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
}

/* Info Cards */
.info-card {
    border-left: 4px solid #007bff;
    border-radius: 0 10px 10px 0;
}

.detail-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
