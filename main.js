const xlsx = require('xlsx');
const fs = require('fs');

const workbook = xlsx.readFile('data/barangAda2.xls');
const sheetName = workbook.SheetNames[0];
const sheet = workbook.Sheets[sheetName];
let rawData = xlsx.utils.sheet_to_json(sheet, { header: 1 }); // ambil array of arrays

let cleanedData = [];
let lastRow = null;

rawData.forEach(row => {
  // Row: [kode, nama barang, satuan, stok, harga beli, harga jual]
  if (!row || row.length === 0) return; // skip kosong
  
  // Gabung jadi string buat cek kata-kata khusus
  let rowText = row.join(" ").trim();

  // Skip baris info halaman / tanggal cetak
  if (rowText.startsWith("Dicetak Tanggal") || rowText.startsWith("Halaman")) return;
  
  // Jika baris diawali kode (angka panjang)
  if (row[0] && /^[0-9]{8,}$/.test(row[0])) {
    // Simpan sebagai baris baru
    lastRow = {
      kode: row[0],
      nama: (row[1] || "").trim(),
      satuan: row[2] || "",
      stok: row[3] || "",
      hargaBeli: row[4] || "",
      hargaJual: row[5] || ""
    };
    cleanedData.push(lastRow);
  } else if (row[0] === undefined && lastRow) {
    // Baris lanjutan nama barang → gabung
    lastRow.nama += " " + (row[1] || "").trim();
  }
});

fs.writeFileSync('barang_clean.json', JSON.stringify(cleanedData, null, 2), 'utf-8');

console.log(`Total barang: ${cleanedData.length}`);
