const xlsx = require('xlsx');
const fs = require('fs');

console.log('=== PEMBERSIH DATA EXCEL BARANG ===');
console.log('Memulai proses...');

let rawData;
try {
  const workbook = xlsx.readFile('data/barangAda2.xls');
  console.log('✓ File Excel berhasil dibaca');

  const sheetName = workbook.SheetNames[0];
  const sheet = workbook.Sheets[sheetName];
  console.log('✓ Mengkonversi sheet ke JSON...');
  rawData = xlsx.utils.sheet_to_json(sheet, { header: 1, defval: "" });
  console.log(`✓ Total baris data: ${rawData.length}`);
} catch (error) {
  console.error('❌ Error membaca file Excel:', error);
  process.exit(1);
}

console.log('Mulai memproses data...');

let cleanedData = [];
let lastRow = null;

rawData.forEach(row => {
  // Row struktur: [empty, kode, nama barang, satuan, stok, harga beli, empty, harga jual]
  if (!row || row.length === 0) return; // skip kosong

  // Gabung jadi string buat cek kata-kata khusus
  let rowText = row.join(" ").trim();

  // Skip baris info halaman / tanggal cetak
  if (rowText.includes("Dicetak Tanggal") || rowText.includes("Halaman")) return;

  // Skip header dan kategori
  if (rowText.includes("LBV SMART") || rowText.includes("Daftar Barang") ||
      rowText.includes("Kode") || rowText.includes("Nama Barang") ||
      rowText.includes("ACCESSORIES") || rowText.includes("AKSESORIS")) return;

  // Jika ada kode di kolom 1 (index 1) dan nama di kolom 2 (index 2)
  if (row[1] && row[2] && (typeof row[1] === 'number' || typeof row[1] === 'string')) {
    // Pastikan ini bukan header dengan mengecek apakah kolom 3 ada satuan
    if (row[3] && typeof row[3] === 'string') {
      // Simpan sebagai baris baru
      lastRow = {
        kode: String(row[1]).trim(),
        nama: String(row[2] || "").trim(),
        satuan: String(row[3] || "").trim(),
        stok: row[4] || 0,
        hargaBeli: row[5] || 0,
        hargaJual: row[6] || row[7] || 0 // kadang ada kolom kosong di tengah
      };
      cleanedData.push(lastRow);
    }
  } else if (!row[1] && row[2] && lastRow) {
    // Baris lanjutan nama barang (kolom 1 kosong, tapi ada nama di kolom 2)
    lastRow.nama += " " + String(row[2] || "").trim();
  }
});

console.log('✓ Selesai memproses data.');
console.log(`✓ Total barang yang berhasil diproses: ${cleanedData.length}`);

// Simpan hasil ke file JSON
fs.writeFileSync('barang_clean.json', JSON.stringify(cleanedData, null, 2), 'utf-8');
console.log('✓ File barang_clean.json berhasil dibuat.');

// Tampilkan statistik
console.log('\n=== STATISTIK HASIL ===');
console.log(`Total barang: ${cleanedData.length}`);
console.log(`Barang dengan nama panjang (>50 karakter): ${cleanedData.filter(item => item.nama.length > 50).length}`);
console.log(`Barang dengan stok 0: ${cleanedData.filter(item => item.stok === 0).length}`);
console.log(`Barang dengan harga jual 0: ${cleanedData.filter(item => item.hargaJual === 0).length}`);

console.log('\n✅ Proses selesai! Data siap digunakan.');
