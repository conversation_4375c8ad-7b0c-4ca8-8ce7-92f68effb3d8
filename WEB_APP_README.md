# 🌐 Web Aplikasi Barang dengan Barcode

Aplikasi web untuk menampilkan data barang dalam bentuk tabel dengan fitur generate barcode menggunakan Node.js, Express, MySQL, dan Bootstrap.

## ✨ Fitur Utama

### 📊 **Tabel Data Barang**
- Menampilkan semua barang dalam tabel yang rapi
- Pagination untuk navigasi data yang besar
- Responsive design untuk semua device
- Sorting dan filtering data

### 🔍 **Pencarian Barang**
- Pencarian berdasarkan kode barang
- Pencarian berdasarkan nama barang
- Real-time search dengan hasil instant
- Filter reset untuk kembali ke semua data

### 📱 **Barcode Generator**
- Generate barcode untuk setiap barang
- Format CODE128 yang standar industri
- Menampilkan kode, nama, dan harga
- Fitur print barcode langsung

### 📋 **Detail Barang**
- Halaman detail lengkap untuk setiap barang
- Informasi stok, harga beli, harga jual
- Analisis margin keuntungan
- Status stok (aman/menipis/habis)
- Quick actions untuk berbagai fungsi

## 🚀 Cara Menjalankan

### 1. **Persiapan**
```bash
# Pastikan data sudah ada di MySQL
node verify_import.js

# Install dependency web
npm install express ejs jsbarcode
```

### 2. **Jalankan Server**
```bash
# Start server
npm start
# atau
node server.js
```

### 3. **Akses Aplikasi**
Buka browser dan akses: **http://localhost:3000**

## 📁 Struktur File Web

```
├── server.js              # Server Express utama
├── package.json           # Dependencies dan scripts
├── views/                 # Template EJS
│   ├── index.ejs         # Halaman utama (tabel barang)
│   └── detail.ejs        # Halaman detail barang
├── public/               # Static files
│   └── style.css         # Custom CSS styling
└── WEB_APP_README.md     # Dokumentasi web app
```

## 🎯 Endpoint API

### **GET /**
- **Deskripsi**: Halaman utama dengan tabel barang
- **Parameter Query**:
  - `page`: Nomor halaman (default: 1)
  - `limit`: Jumlah data per halaman (default: 50)
  - `search`: Kata kunci pencarian

### **GET /api/barang**
- **Deskripsi**: API untuk mendapatkan data barang (JSON)
- **Parameter Query**:
  - `search`: Kata kunci pencarian
  - `limit`: Batas jumlah data (default: 100)

### **GET /barcode/:kode**
- **Deskripsi**: API untuk mendapatkan data barcode
- **Response**: JSON dengan data barang untuk generate barcode

### **GET /barang/:kode**
- **Deskripsi**: Halaman detail barang
- **Parameter**: `kode` - Kode barang

## 🎨 Fitur UI/UX

### **Dashboard Stats**
- Total barang dalam database
- Status pencarian aktif
- Informasi pagination
- Indikator barcode ready

### **Tabel Interaktif**
- Hover effects pada baris
- Color coding untuk status stok
- Responsive columns
- Sticky header saat scroll

### **Modal Barcode**
- Pop-up modal untuk menampilkan barcode
- Loading animation saat generate
- Print function terintegrasi
- Responsive design

### **Halaman Detail**
- Layout card yang menarik
- Analisis margin keuntungan otomatis
- Status stok dengan color coding
- Quick actions untuk fungsi cepat

## 🔧 Konfigurasi

### **Database Connection**
Edit di `server.js`:
```javascript
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Sesuaikan password MySQL
  database: 'cashier'
};
```

### **Server Port**
Default port: `3000`
Untuk mengubah, edit di `server.js`:
```javascript
const PORT = 3000; // Ubah sesuai kebutuhan
```

## 📱 Responsive Design

### **Desktop (>992px)**
- Tabel full width dengan semua kolom
- Sidebar detail di halaman detail
- Modal barcode ukuran besar

### **Tablet (768px-992px)**
- Tabel dengan scroll horizontal
- Layout stack di halaman detail
- Modal barcode medium

### **Mobile (<768px)**
- Tabel compact dengan font kecil
- Button size disesuaikan
- Modal barcode full width

## 🖨️ Fitur Print

### **Print Barcode**
- Print individual barcode dari modal
- Print barcode dari halaman detail
- Format print yang rapi dan standar
- Optimized untuk printer thermal

### **Print Table** (Future)
- Print seluruh tabel barang
- Print hasil pencarian
- Format landscape untuk tabel lebar

## 🔍 Pencarian & Filter

### **Pencarian Dasar**
- Cari berdasarkan kode barang
- Cari berdasarkan nama barang
- Case-insensitive search
- Partial matching

### **Filter Lanjutan** (Future Enhancement)
- Filter berdasarkan kategori
- Filter berdasarkan range harga
- Filter berdasarkan status stok
- Filter berdasarkan satuan

## 📊 Statistik & Analytics

### **Dashboard Metrics**
- Total barang dalam database
- Jumlah hasil pencarian
- Status pagination
- Indikator sistem

### **Detail Analytics**
- Margin keuntungan per barang
- Persentase margin
- Nilai total stok
- Status stok dengan alert

## 🚀 Performance

### **Optimisasi Database**
- Connection pooling untuk MySQL
- Prepared statements untuk keamanan
- Pagination untuk data besar
- Index pada kolom pencarian

### **Optimisasi Frontend**
- CDN untuk Bootstrap dan FontAwesome
- Minified CSS dan JS
- Lazy loading untuk barcode
- Responsive images

## 🔒 Keamanan

### **Input Validation**
- Sanitasi input pencarian
- Parameter validation
- SQL injection prevention
- XSS protection

### **Error Handling**
- Graceful error handling
- User-friendly error messages
- Database connection error handling
- 404 handling untuk barang tidak ditemukan

## 🎯 Penggunaan untuk Kasir

### **Workflow Kasir**
1. **Cari Barang**: Gunakan pencarian untuk menemukan barang
2. **Lihat Detail**: Klik detail untuk informasi lengkap
3. **Generate Barcode**: Klik tombol barcode untuk membuat barcode
4. **Print Barcode**: Print barcode untuk label harga
5. **Cek Stok**: Monitor status stok real-time

### **Manajemen Inventory**
- Monitor barang dengan stok menipis
- Analisis margin keuntungan
- Tracking nilai total inventory
- Export data untuk laporan

## 🔄 Update & Maintenance

### **Update Data**
Data akan otomatis terupdate sesuai dengan database MySQL. Untuk update data:
1. Update database menggunakan script import
2. Refresh halaman web untuk melihat perubahan

### **Backup**
- Backup database MySQL secara berkala
- Backup file aplikasi web
- Monitor log error untuk troubleshooting

## 📞 Support

Jika ada masalah atau pertanyaan:
1. Cek log server di terminal
2. Cek koneksi database MySQL
3. Pastikan semua dependency terinstall
4. Restart server jika diperlukan

---

## 🎉 **Web Aplikasi Siap Digunakan!**

✅ **URL**: http://localhost:3000  
✅ **Database**: 41,568 barang siap ditampilkan  
✅ **Barcode**: Generate dan print ready  
✅ **Responsive**: Desktop, tablet, mobile friendly  
✅ **Fast**: Optimized untuk performa tinggi  

**Perfect untuk aplikasi kasir dan manajemen inventory!** 🛒💰
