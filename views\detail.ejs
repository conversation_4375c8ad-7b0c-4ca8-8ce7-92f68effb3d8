<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Barang - <%= barang.nama %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .detail-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .info-card {
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-dark bg-primary">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-shopping-cart me-2"></i>
                Detail Barang
            </span>
            <a href="/" class="btn btn-light">
                <i class="fas fa-arrow-left me-1"></i> Kembali
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <!-- Detail Barang -->
                <div class="card detail-card mb-4">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fas fa-box me-2"></i>
                            <%= barang.nama %>
                        </h2>
                        <p class="card-text">
                            <code class="bg-light text-dark p-2 rounded">Kode: <%= barang.kode %></code>
                        </p>
                    </div>
                </div>

                <!-- Informasi Detail -->
                <div class="card info-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informasi Detail
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><i class="fas fa-barcode me-2"></i>Kode Barang:</strong></td>
                                        <td><code><%= barang.kode %></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong><i class="fas fa-tag me-2"></i>Nama Barang:</strong></td>
                                        <td><%= barang.nama %></td>
                                    </tr>
                                    <tr>
                                        <td><strong><i class="fas fa-balance-scale me-2"></i>Satuan:</strong></td>
                                        <td><span class="badge bg-secondary"><%= barang.satuan %></span></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><i class="fas fa-boxes me-2"></i>Stok:</strong></td>
                                        <td>
                                            <% if (barang.stok > 0) { %>
                                                <span class="badge bg-success fs-6"><%= barang.stok %> <%= barang.satuan %></span>
                                            <% } else { %>
                                                <span class="badge bg-danger fs-6">Stok Habis</span>
                                            <% } %>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><i class="fas fa-money-bill me-2"></i>Harga Beli:</strong></td>
                                        <td><span class="text-warning">Rp <%= parseInt(barang.hargaBeli).toLocaleString() %></span></td>
                                    </tr>
                                    <tr>
                                        <td><strong><i class="fas fa-dollar-sign me-2"></i>Harga Jual:</strong></td>
                                        <td><span class="text-success fs-5">Rp <%= parseInt(barang.hargaJual).toLocaleString() %></span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analisis -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Analisis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>Margin Keuntungan</h6>
                                    <h4 class="text-success">
                                        Rp <%= (parseInt(barang.hargaJual) - parseInt(barang.hargaBeli)).toLocaleString() %>
                                    </h4>
                                    <small class="text-muted">per <%= barang.satuan %></small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>Persentase Margin</h6>
                                    <h4 class="text-info">
                                        <%= (((parseInt(barang.hargaJual) - parseInt(barang.hargaBeli)) / parseInt(barang.hargaBeli)) * 100).toFixed(1) %>%
                                    </h4>
                                    <small class="text-muted">dari harga beli</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>Nilai Total Stok</h6>
                                    <h4 class="text-primary">
                                        Rp <%= (parseInt(barang.stok) * parseInt(barang.hargaJual)).toLocaleString() %>
                                    </h4>
                                    <small class="text-muted">nilai jual</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Barcode -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-barcode me-2"></i>
                            Barcode
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div id="barcodeContainer">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Generating barcode...</p>
                        </div>
                        <button class="btn btn-primary mt-3" onclick="printBarcode()" id="printBtn" style="display: none;">
                            <i class="fas fa-print me-1"></i> Print Barcode
                        </button>
                    </div>
                </div>

                <!-- Status Stok -->
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Status Stok
                        </h5>
                    </div>
                    <div class="card-body">
                        <% if (barang.stok > 10) { %>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Stok Aman</strong><br>
                                Stok tersedia: <%= barang.stok %> <%= barang.satuan %>
                            </div>
                        <% } else if (barang.stok > 0) { %>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Stok Menipis</strong><br>
                                Sisa stok: <%= barang.stok %> <%= barang.satuan %>
                            </div>
                        <% } else { %>
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>Stok Habis</strong><br>
                                Perlu restock segera!
                            </div>
                        <% } %>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="showBarcode()">
                                <i class="fas fa-barcode me-1"></i> Generate Barcode
                            </button>
                            <button class="btn btn-info" onclick="copyKode()">
                                <i class="fas fa-copy me-1"></i> Copy Kode
                            </button>
                            <a href="/" class="btn btn-secondary">
                                <i class="fas fa-list me-1"></i> Lihat Semua Barang
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        // Auto-load barcode when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadBarcode();
        });

        function loadBarcode() {
            const container = document.getElementById('barcodeContainer');
            const printBtn = document.getElementById('printBtn');
            
            fetch(`/barcode/<%= barang.kode %>`)
                .then(response => response.json())
                .then(data => {
                    container.innerHTML = `
                        <div class="mb-3">
                            <svg id="barcode-detail" class="img-fluid" style="max-width: 100%;"></svg>
                        </div>
                        <p class="mb-0">
                            <strong>Kode:</strong> ${data.kode}<br>
                            <strong>Harga:</strong> <span class="text-success">Rp ${parseInt(data.hargaJual).toLocaleString()}</span>
                        </p>
                    `;

                    // Generate barcode using JsBarcode
                    JsBarcode("#barcode-detail", data.kode, {
                        format: "CODE128",
                        width: 2,
                        height: 60,
                        displayValue: true,
                        fontSize: 12,
                        textMargin: 5
                    });

                    printBtn.style.display = 'block';
                })
                .catch(error => {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error loading barcode
                        </div>
                    `;
                });
        }

        function showBarcode() {
            loadBarcode();
        }

        function printBarcode() {
            const content = document.getElementById('barcodeContainer').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Print Barcode - <%= barang.nama %></title>
                        <style>
                            body { 
                                font-family: Arial, sans-serif; 
                                text-align: center; 
                                padding: 20px;
                            }
                            .barcode-print {
                                border: 2px solid #000;
                                padding: 20px;
                                margin: 20px auto;
                                max-width: 400px;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="barcode-print">
                            <h3><%= barang.nama %></h3>
                            ${content}
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function copyKode() {
            navigator.clipboard.writeText('<%= barang.kode %>').then(function() {
                alert('Kode barang berhasil disalin: <%= barang.kode %>');
            });
        }
    </script>
</body>
</html>
