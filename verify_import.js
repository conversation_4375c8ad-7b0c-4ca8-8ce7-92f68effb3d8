const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'cashier'
};

async function verifyImport() {
  let connection;
  
  try {
    console.log('=== VERIFIKASI DATA IMPORT ===');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ Koneksi berhasil');
    
    // Total data
    const [totalRows] = await connection.query('SELECT COUNT(*) as total FROM barang');
    console.log(`\n📊 Total data di database: ${totalRows[0].total.toLocaleString()}`);
    
    // Contoh data
    console.log('\n=== CONTOH DATA ===');
    const [sampleRows] = await connection.query('SELECT * FROM barang LIMIT 10');
    sampleRows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.kode}] ${row.nama}`);
      console.log(`   Satuan: ${row.satuan}, Stok: ${row.stok}, Harga Beli: Rp ${parseInt(row.hargaBeli).toLocaleString()}, Harga Jual: Rp ${parseInt(row.hargaJual).toLocaleString()}`);
    });
    
    // Statistik
    console.log('\n=== STATISTIK ===');
    
    // Barang dengan stok > 0
    const [stokRows] = await connection.query('SELECT COUNT(*) as total FROM barang WHERE stok > 0');
    console.log(`Barang dengan stok > 0: ${stokRows[0].total.toLocaleString()}`);
    
    // Barang dengan stok = 0
    const [stokKosongRows] = await connection.query('SELECT COUNT(*) as total FROM barang WHERE stok = 0');
    console.log(`Barang dengan stok = 0: ${stokKosongRows[0].total.toLocaleString()}`);
    
    // Barang dengan harga jual > 100000
    const [hargaTinggiRows] = await connection.query('SELECT COUNT(*) as total FROM barang WHERE hargaJual > 100000');
    console.log(`Barang dengan harga > Rp 100.000: ${hargaTinggiRows[0].total.toLocaleString()}`);
    
    // Total nilai stok
    const [nilaiStokRows] = await connection.query('SELECT SUM(stok * hargaBeli) as total_nilai FROM barang');
    const totalNilaiStok = nilaiStokRows[0].total_nilai || 0;
    console.log(`Total nilai stok: Rp ${parseInt(totalNilaiStok).toLocaleString()}`);
    
    // Barang dengan nama terpanjang
    console.log('\n=== BARANG DENGAN NAMA TERPANJANG ===');
    const [namaPanjangRows] = await connection.query('SELECT kode, nama, LENGTH(nama) as panjang FROM barang ORDER BY LENGTH(nama) DESC LIMIT 5');
    namaPanjangRows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.kode}] ${row.nama} (${row.panjang} karakter)`);
    });
    
    // Barang dengan stok terbanyak
    console.log('\n=== BARANG DENGAN STOK TERBANYAK ===');
    const [stokTerbanyakRows] = await connection.query('SELECT kode, nama, stok FROM barang ORDER BY stok DESC LIMIT 5');
    stokTerbanyakRows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.kode}] ${row.nama} - Stok: ${row.stok.toLocaleString()}`);
    });
    
    // Barang dengan harga termahal
    console.log('\n=== BARANG TERMAHAL ===');
    const [hargaTermahalRows] = await connection.query('SELECT kode, nama, hargaJual FROM barang ORDER BY hargaJual DESC LIMIT 5');
    hargaTermahalRows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.kode}] ${row.nama} - Harga: Rp ${parseInt(row.hargaJual).toLocaleString()}`);
    });
    
    console.log('\n✅ Verifikasi selesai!');
    console.log('\n🎉 Data berhasil diimpor ke database MySQL!');
    console.log('📝 Database: cashier');
    console.log('📝 Tabel: barang');
    console.log(`📝 Total data: ${totalRows[0].total.toLocaleString()} barang`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Koneksi ditutup');
    }
  }
}

verifyImport();
