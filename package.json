{"name": "cashiergum", "version": "1.0.0", "description": "Aplikasi Web Barang dengan Barcode", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["<PERSON><PERSON>", "barcode", "inventory"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"canvas": "^3.1.2", "ejs": "^3.1.10", "express": "^4.21.2", "jsbarcode": "^3.12.1", "mysql2": "^3.14.3", "xlsx": "^0.18.5"}}