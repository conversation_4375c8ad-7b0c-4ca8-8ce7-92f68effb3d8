const mysql = require('mysql2/promise');

// Konfigurasi database (tanpa database spesifik untuk membuat database baru)
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Ganti dengan password MySQL Anda
};

async function setupDatabase() {
  let connection;
  
  try {
    console.log('=== SETUP DATABASE CASHIER ===');
    
    // Koneksi ke MySQL server
    console.log('🔌 Menghubungkan ke MySQL server...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ Koneksi MySQL berhasil');
    
    // Buat database cashier
    console.log('🏗️ Membuat database cashier...');
    await connection.execute('CREATE DATABASE IF NOT EXISTS cashier CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✓ Database cashier berhasil dibuat');
    
    // Gunakan database cashier
    await connection.execute('USE cashier');
    
    // Buat tabel barang
    console.log('🏗️ Membuat tabel barang...');
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS barang (
        id INT AUTO_INCREMENT PRIMARY KEY,
        kode VARCHAR(50) NOT NULL UNIQUE,
        nama VARCHAR(255) NOT NULL,
        satuan VARCHAR(50) NOT NULL,
        stok INT DEFAULT 0,
        harga_beli DECIMAL(15,2) DEFAULT 0,
        harga_jual DECIMAL(15,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_kode (kode),
        INDEX idx_nama (nama),
        INDEX idx_stok (stok)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createTableQuery);
    console.log('✓ Tabel barang berhasil dibuat');
    
    // Tampilkan struktur tabel
    console.log('\n📋 Struktur tabel barang:');
    const [columns] = await connection.execute('DESCRIBE barang');
    columns.forEach(col => {
      console.log(`   ${col.Field} - ${col.Type} ${col.Null === 'NO' ? '(NOT NULL)' : ''} ${col.Key ? `(${col.Key})` : ''}`);
    });
    
    console.log('\n✅ Setup database selesai!');
    console.log('📝 Database: cashier');
    console.log('📝 Tabel: barang');
    console.log('\n🚀 Sekarang Anda bisa menjalankan: node import_to_mysql.js');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Pastikan MySQL server sudah berjalan');
      console.log('   - Windows: Jalankan XAMPP/WAMP');
      console.log('   - Linux: sudo service mysql start');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Periksa username/password MySQL di file ini');
    }
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Koneksi database ditutup');
    }
  }
}

// Jalankan setup
setupDatabase();
