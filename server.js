const express = require('express');
const mysql = require('mysql2/promise');
const path = require('path');

const app = express();
const PORT = 3000;

// Konfigurasi database
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'cashier'
};

// Setup view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Static files
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Database connection pool
const pool = mysql.createPool(dbConfig);

// Routes
app.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const search = req.query.search || '';
    const offset = (page - 1) * limit;
    
    let whereClause = '';
    let queryParams = [];
    
    if (search) {
      whereClause = 'WHERE kode LIKE ? OR nama LIKE ?';
      queryParams = [`%${search}%`, `%${search}%`];
    }
    
    // Get total count
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM barang ${whereClause}`,
      queryParams
    );
    const totalItems = countResult[0].total;
    const totalPages = Math.ceil(totalItems / limit);
    
    // Get data
    const [rows] = await pool.query(
      `SELECT * FROM barang ${whereClause} ORDER BY nama ASC LIMIT ? OFFSET ?`,
      [...queryParams, limit, offset]
    );
    
    res.render('index', {
      barang: rows,
      currentPage: page,
      totalPages: totalPages,
      totalItems: totalItems,
      limit: limit,
      search: search,
      hasNext: page < totalPages,
      hasPrev: page > 1
    });
    
  } catch (error) {
    console.error('Error:', error);
    res.status(500).send('Database error');
  }
});

// API untuk mendapatkan data barang
app.get('/api/barang', async (req, res) => {
  try {
    const search = req.query.search || '';
    const limit = parseInt(req.query.limit) || 100;
    
    let whereClause = '';
    let queryParams = [];
    
    if (search) {
      whereClause = 'WHERE kode LIKE ? OR nama LIKE ?';
      queryParams = [`%${search}%`, `%${search}%`];
    }
    
    const [rows] = await pool.query(
      `SELECT * FROM barang ${whereClause} ORDER BY nama ASC LIMIT ?`,
      [...queryParams, limit]
    );
    
    res.json(rows);
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Route untuk generate barcode
app.get('/barcode/:kode', async (req, res) => {
  try {
    const kode = req.params.kode;

    // Get barang data
    const [rows] = await pool.query('SELECT * FROM barang WHERE kode = ?', [kode]);

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Barang tidak ditemukan' });
    }

    const barang = rows[0];

    // Return data untuk generate barcode di frontend
    res.json({
      kode: barang.kode,
      nama: barang.nama,
      hargaJual: barang.hargaJual,
      satuan: barang.satuan,
      stok: barang.stok
    });

  } catch (error) {
    console.error('Error getting barcode data:', error);
    res.status(500).json({ error: 'Error getting barcode data' });
  }
});

// Route untuk halaman detail barang
app.get('/barang/:kode', async (req, res) => {
  try {
    const kode = req.params.kode;
    
    const [rows] = await pool.query('SELECT * FROM barang WHERE kode = ?', [kode]);
    
    if (rows.length === 0) {
      return res.status(404).send('Barang tidak ditemukan');
    }
    
    res.render('detail', { barang: rows[0] });
    
  } catch (error) {
    console.error('Error:', error);
    res.status(500).send('Database error');
  }
});

// Start server
app.listen(PORT, () => {
  console.log('=== WEB APLIKASI BARANG DENGAN BARCODE ===');
  console.log(`🚀 Server berjalan di: http://localhost:${PORT}`);
  console.log('📊 Fitur yang tersedia:');
  console.log('   - Tabel data barang dengan pagination');
  console.log('   - Pencarian barang');
  console.log('   - Generate barcode untuk setiap barang');
  console.log('   - Detail barang');
  console.log('\n💡 Buka browser dan akses: http://localhost:3000');
});
