const fs = require('fs');

console.log('=== ANALISIS DATA BARANG ===');

try {
  const data = JSON.parse(fs.readFileSync('barang_clean_improved.json', 'utf-8'));
  
  console.log(`Total barang: ${data.length}`);
  
  // Cek barang dengan masalah
  const masalahKode = data.filter(item => !item.kode || item.kode.trim() === '');
  const masalahNama = data.filter(item => !item.nama || item.nama.trim() === '');
  const masalahSatuan = data.filter(item => !item.satuan || item.satuan.trim() === '');
  const namaPanjang = data.filter(item => item.nama.length > 50);
  const stokKosong = data.filter(item => item.stok === 0 || item.stok === '');
  
  console.log('\n=== MASALAH YANG DITEMUKAN ===');
  console.log(`Barang tanpa kode: ${masalahKode.length}`);
  console.log(`Barang tanpa nama: ${masalahNama.length}`);
  console.log(`Barang tanpa satuan: ${masalahSatuan.length}`);
  console.log(`Barang dengan nama panjang (>50 char): ${namaPanjang.length}`);
  console.log(`Barang dengan stok 0: ${stokKosong.length}`);
  
  // Tampilkan contoh barang dengan nama panjang
  if (namaPanjang.length > 0) {
    console.log('\n=== CONTOH BARANG DENGAN NAMA PANJANG ===');
    namaPanjang.slice(0, 10).forEach((item, index) => {
      console.log(`${index + 1}. [${item.kode}] ${item.nama} (${item.nama.length} karakter)`);
      console.log(`   Satuan: ${item.satuan}, Stok: ${item.stok}`);
    });
  }
  
  // Tampilkan contoh barang dengan stok 0
  if (stokKosong.length > 0) {
    console.log('\n=== CONTOH BARANG DENGAN STOK 0 ===');
    stokKosong.slice(0, 5).forEach((item, index) => {
      console.log(`${index + 1}. [${item.kode}] ${item.nama}`);
      console.log(`   Satuan: ${item.satuan}, Stok: ${item.stok}, Harga: ${item.hargaJual}`);
    });
  }
  
  // Cek duplikasi kode
  const kodeMap = {};
  const duplikat = [];
  data.forEach(item => {
    if (kodeMap[item.kode]) {
      duplikat.push(item.kode);
    } else {
      kodeMap[item.kode] = true;
    }
  });
  
  console.log(`\nKode barang duplikat: ${duplikat.length}`);
  if (duplikat.length > 0) {
    console.log('Contoh kode duplikat:', duplikat.slice(0, 5));
  }
  
  // Statistik harga
  const hargaKosong = data.filter(item => item.hargaJual === 0 || item.hargaJual === '');
  const hargaTinggi = data.filter(item => item.hargaJual > 1000000);
  
  console.log(`\nBarang tanpa harga jual: ${hargaKosong.length}`);
  console.log(`Barang dengan harga > 1 juta: ${hargaTinggi.length}`);
  
  console.log('\n✅ Analisis selesai!');
  
} catch (error) {
  console.error('❌ Error membaca file:', error);
}
