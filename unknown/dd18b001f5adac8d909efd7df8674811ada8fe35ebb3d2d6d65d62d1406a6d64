[{"name": "code39", "names": "CODE39", "barcodeFile": "./CODE39"}, {"name": "code128", "names": ["CODE128, CODE128A, CODE128B, CODE128C"], "barcodeFile": "./CODE128"}, {"name": "ean-upc", "names": "EAN13, EA<PERSON>8, <PERSON><PERSON>5, EAN2, UPC", "barcodeFile": "./EAN_UPC"}, {"name": "itf", "names": "ITF, ITF14", "barcodeFile": "./ITF"}, {"name": "msi", "names": "MSI, MSI10, MSI11, MSI1010, MSI1110", "barcodeFile": "./MSI"}, {"name": "pharmacode", "names": "pharmacode", "barcodeFile": "./pharmacode"}, {"name": "codabar", "names": "codabar", "barcodeFile": "./codabar"}, {"name": "code93", "names": ["CODE93", "CODE93FullASCII"], "barcodeFile": "./CODE93"}]