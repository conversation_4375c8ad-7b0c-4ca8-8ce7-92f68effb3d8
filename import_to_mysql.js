const mysql = require('mysql2/promise');
const fs = require('fs');

// Konfigurasi database
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Ganti dengan password MySQL Anda
  database: 'cashier'
};

async function importToMySQL() {
  let connection;
  
  try {
    console.log('=== IMPORT DATA BARANG KE MYSQL ===');
    
    // Baca data JSON
    console.log('📖 Membaca file JSON...');
    const jsonData = fs.readFileSync('barang_clean_improved.json', 'utf-8');
    const barangData = JSON.parse(jsonData);
    console.log(`✓ Berhasil membaca ${barangData.length} data barang`);
    
    // Koneksi ke database
    console.log('🔌 Menghubungkan ke database MySQL...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ Koneksi database berhasil');
    
    // Buat tabel jika belum ada
    console.log('🏗️ Memastikan tabel barang ada...');
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS barang (
        id INT AUTO_INCREMENT PRIMARY KEY,
        kode VARCHAR(50) NOT NULL UNIQUE,
        nama VARCHAR(255) NOT NULL,
        satuan VARCHAR(50) NOT NULL,
        stok INT DEFAULT 0,
        harga_beli DECIMAL(15,2) DEFAULT 0,
        harga_jual DECIMAL(15,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_kode (kode),
        INDEX idx_nama (nama)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await connection.query(createTableQuery);
    console.log('✓ Tabel barang siap');
    
    // Hapus data lama (opsional)
    console.log('🗑️ Menghapus data lama...');
    await connection.query('DELETE FROM barang');
    console.log('✓ Data lama dihapus');
    
    // Insert data dalam batch
    console.log('📥 Mengimpor data barang...');
    const batchSize = 1000;
    let imported = 0;
    let errors = 0;
    
    for (let i = 0; i < barangData.length; i += batchSize) {
      const batch = barangData.slice(i, i + batchSize);
      
      const insertQuery = `
        INSERT INTO barang (kode, nama, satuan, stok, hargaBeli, hargaJual)
        VALUES ?
        ON DUPLICATE KEY UPDATE
        nama = VALUES(nama),
        satuan = VALUES(satuan),
        stok = VALUES(stok),
        hargaBeli = VALUES(hargaBeli),
        hargaJual = VALUES(hargaJual)
      `;
      
      const values = batch.map(item => [
        item.kode,
        item.nama,
        item.satuan,
        parseInt(item.stok) || 0,
        parseFloat(item.hargaBeli) || 0,
        parseFloat(item.hargaJual) || 0
      ]);
      
      try {
        await connection.query(insertQuery, [values]);
        imported += batch.length;
        console.log(`✓ Batch ${Math.floor(i/batchSize) + 1}: ${imported}/${barangData.length} data diimpor`);
      } catch (error) {
        console.error(`❌ Error pada batch ${Math.floor(i/batchSize) + 1}:`, error.message);
        errors += batch.length;
      }
    }
    
    // Verifikasi hasil
    console.log('\n📊 Verifikasi hasil...');
    const [rows] = await connection.query('SELECT COUNT(*) as total FROM barang');
    const totalInDB = rows[0].total;

    console.log('\n=== HASIL IMPORT ===');
    console.log(`✓ Total data berhasil diimpor: ${imported}`);
    console.log(`❌ Total data error: ${errors}`);
    console.log(`📊 Total data di database: ${totalInDB}`);

    // Tampilkan contoh data
    console.log('\n=== CONTOH DATA DI DATABASE ===');
    const [sampleRows] = await connection.query('SELECT * FROM barang LIMIT 5');
    sampleRows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.kode}] ${row.nama}`);
      console.log(`   Satuan: ${row.satuan}, Stok: ${row.stok}, Harga: Rp ${parseInt(row.hargaJual).toLocaleString()}`);
    });
    
    console.log('\n✅ Import selesai!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Pastikan MySQL server sudah berjalan');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Periksa username/password MySQL');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Database "cashier" belum ada. Buat dulu dengan:');
      console.log('   CREATE DATABASE cashier;');
    }
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Koneksi database ditutup');
    }
  }
}

// Jalankan import
importToMySQL();
